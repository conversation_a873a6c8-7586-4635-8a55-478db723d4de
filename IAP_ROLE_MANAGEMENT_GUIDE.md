# IAP Role Management System

## Overview

This system handles In-App Purchase (IAP) flow with dynamic role management. Users start as 'MEMBER' and can gain 'OWNER' access through subscription purchase or free trial.

## User Role Structure

### Database Fields
- `userType`: JSON array storing all available roles (e.g., `['MEMBER', 'OWNER']`)
- `currentType`: Active role enum (MEMBER/OWNER)

### Role Flow
1. **Initial State**: `userType: ['MEMBER']`, `currentType: MEMBER`
2. **After Purchase/Trial**: `userType: ['MEMBER', 'OWNER']`, `currentType: OWNER`
3. **After Cancellation**: `userType: ['MEMBER']`, `currentType: MEMBER`

## API Endpoints

### 1. Start Free Trial
```
POST /profile/start-trial?userId={userId}
Body: { "communityId": "community-uuid" }
```
- Starts 14-day free trial
- Adds 'OWNER' to userType array
- Switches currentType to OWNER
- Creates trial purchase record

### 2. Purchase Subscription
```
POST /profile/purchase-subscription?userId={userId}
Body: {
  "communityId": "community-uuid",
  "platform": "android|ios",
  "receiptData": "receipt-string",
  "packageName": "com.example.app" // optional for Android
}
```
- Validates purchase receipt (implement IAP validation)
- Adds 'OWNER' to userType if not present
- Switches currentType to OWNER
- Creates purchase record

### 3. Switch User Role
```
PATCH /profile/switch-role?userId={userId}
Body: { "targetRole": "MEMBER|OWNER" }
```
- Switches between available roles
- Validates user has access to target role
- Checks active subscription for OWNER role

### 4. Cancel Subscription
```
DELETE /profile/cancel-subscription/{communityId}?userId={userId}
```
- Cancels specific community subscription
- Removes OWNER role if no other active subscriptions
- Switches to MEMBER role if OWNER removed

### 5. Get Subscription Status
```
GET /profile/subscription-status?userId={userId}
```
- Returns user's current roles and subscription status
- Shows active subscriptions
- Indicates available role switches

### 6. Handle Expired Subscriptions
```
POST /profile/handle-expired-subscriptions?userId={userId}
```
- Checks for expired subscriptions
- Removes OWNER role if no active subscriptions
- Switches to MEMBER role

## Implementation Details

### Key Features

1. **Multi-Community Support**: Users can have subscriptions to multiple communities
2. **Role Persistence**: OWNER role persists as long as any subscription is active
3. **Automatic Cleanup**: Expired subscriptions trigger role removal
4. **Role Switching**: Users can switch between MEMBER and OWNER roles after purchase

### Database Schema

```sql
-- User table
userType: JSON -- ['MEMBER'] or ['MEMBER', 'OWNER']
currentType: ENUM -- 'MEMBER' or 'OWNER'

-- MemberPurchase table
memberId: String
communityId: String
membershipStatus: ENUM -- 'ACTIVE', 'EXPIRED', 'REVOKED'
expiresAt: DateTime -- null for lifetime, date for trials/subscriptions
```

### Business Logic

1. **Trial Logic**: 14-day trial creates purchase record with expiration
2. **Purchase Logic**: Validates receipt and creates permanent subscription
3. **Cancellation Logic**: Revokes subscription but keeps role if other subscriptions exist
4. **Expiration Logic**: Removes OWNER role when all subscriptions expire

## Integration with Existing Code

### Required Modules
- `MemberPurchaseCoreModule` - for subscription management
- `UserCoreModule` - for user role updates
- `CertificateCoreModule` - existing certificate functionality

### Service Dependencies
```typescript
constructor(
  private readonly userCoreService: UserCoreService,
  private readonly certificateCoreService: CertificateCoreService,
  private readonly memberPurchaseCoreService: MemberPurchaseCoreService,
) {}
```

## Usage Examples

### Frontend Flow

1. **Show Upgrade Screen**: Check subscription status to show appropriate UI
2. **Start Trial**: Call start-trial endpoint when user taps "Start 14-Day Trial"
3. **Purchase**: Call purchase-subscription with receipt after successful IAP
4. **Role Switch**: Allow user to switch between MEMBER/OWNER views
5. **Cancel**: Handle cancellation through cancel-subscription endpoint

### Backend Integration

1. **Scheduled Jobs**: Run handle-expired-subscriptions periodically
2. **Webhook Integration**: Handle app store webhooks for subscription changes
3. **Role Validation**: Check currentType in middleware for protected routes

## Error Handling

- **Duplicate Subscriptions**: Prevents multiple active subscriptions per community
- **Invalid Receipts**: Validates IAP receipts before granting access
- **Role Restrictions**: Ensures users can only switch to available roles
- **Authorization**: Validates user can only modify their own data

## Security Considerations

1. **Receipt Validation**: Always validate IAP receipts with app stores
2. **User Authorization**: Verify user can only access their own data
3. **Role Verification**: Check active subscriptions before allowing OWNER actions
4. **Audit Trail**: Track all subscription and role changes

## Testing

1. **Unit Tests**: Test role transitions and business logic
2. **Integration Tests**: Test with mock IAP receipts
3. **E2E Tests**: Test complete user journey from trial to purchase
