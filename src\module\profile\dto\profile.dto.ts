import { IsOptional, IsString, IsDateString, IsUrl, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateMemberProfileDto {
  @ApiProperty()
  @IsString()
  firstName?: string;

  @ApiProperty()
  @IsString()
  lastName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  countryCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  contact?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  profileImage?: string;
}

export class UpdateOwnerProfileDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  profileImage?: string;
}

export class CreateCertificateDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsUrl()
  url: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsString()
  // issuer: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsDateString()
  // issuedDate?: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsDateString()
  // expiryDate?: string;

  // @ApiProperty({ required: false })
  // @IsOptional()
  // @IsString()
  // description?: string;
}

export class UpdateNotificationPreferenceDto {
  @ApiProperty({ example: true })
  @IsBoolean()
  isPushNotificationsEnabled: boolean;
}

export class StartTrialDto {
  @ApiProperty({ description: 'Community ID for the trial subscription' })
  @IsString()
  communityId: string;
}

export class PurchaseSubscriptionDto {
  @ApiProperty({ description: 'Community ID for the subscription' })
  @IsString()
  communityId: string;

  @ApiProperty({ description: 'Platform (android or ios)' })
  @IsString()
  platform: 'android' | 'ios';

  @ApiProperty({ description: 'Receipt data or purchase token' })
  @IsString()
  receiptData: string;

  @ApiProperty({ description: 'Package name (required for Android)', required: false })
  @IsOptional()
  @IsString()
  packageName?: string;
}

export class SwitchUserRoleDto {
  @ApiProperty({ description: 'Target user role', enum: ['MEMBER', 'OWNER'] })
  @IsString()
  targetRole: 'MEMBER' | 'OWNER';
}