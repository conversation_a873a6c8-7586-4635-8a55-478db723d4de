import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ProfileService } from './profile.service';
import {
  CreateCertificateDto,
  UpdateMemberProfileDto,
  UpdateOwnerProfileDto,
  UpdateNotificationPreferenceDto,
  StartTrialDto,
  PurchaseSubscriptionDto,
  SwitchUserRoleDto,
} from './dto/profile.dto';
import { UserLoginJwtGuard } from 'src/module/auth/guards/firebase-auth.guard';
import { GetUserSession } from 'src/shared/decorators/user-session.decorator';
import { UserSessionType } from 'src/shared/types/user-session.type';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
} from '@nestjs/swagger';

@ApiTags('Profile')
@ApiBearerAuth()
@UseGuards(UserLoginJwtGuard)
@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Patch('member')
  @ApiOperation({ summary: `Update member profile` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async updateMemberProfile(
    @Query('userId') userId: string,
    @Body() dto: UpdateMemberProfileDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.updateMemberProfile(userId, dto, sessionData);
  }

  @Patch('owner')
  @ApiOperation({ summary: `Update owner profile` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async updateOwnerProfile(
    @Query('userId') userId: string,
    @Body() dto: UpdateOwnerProfileDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.updateOwnerProfile(userId, dto, sessionData);
  }

  @Patch('notification-preference')
  @ApiOperation({ summary: `Update notification preference` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async updateNotificationPreference(
    @Query('userId') userId: string,
    @Body() dto: UpdateNotificationPreferenceDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.updateNotificationPreference(
      userId,
      dto,
      sessionData,
    );
  }

  @Post('certificate')
  @ApiOperation({ summary: `Add certificate` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async addCertificate(
    @Query('userId') userId: string,
    @Body() dto: CreateCertificateDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.addCertificate(userId, dto, sessionData);
  }

  @Get('certificate')
  @ApiOperation({ summary: `Get all certificates` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async getCertificates(
    @Query('userId') userId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.getCertificates(userId, sessionData);
  }

  @Delete('certificate/:certificateId')
  @ApiOperation({ summary: `Delete certificate` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async deleteCertificate(
    @Query('userId') userId: string,
    @Param('certificateId') certificateId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.deleteCertificate(userId, certificateId, sessionData);
  }

  @Post('start-trial')
  @ApiOperation({ summary: `Start 14-day free trial and add OWNER role` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async startFreeTrial(
    @Query('userId') userId: string,
    @Body() dto: StartTrialDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.startFreeTrial(userId, dto, sessionData);
  }

  @Post('purchase-subscription')
  @ApiOperation({ summary: `Purchase subscription and add OWNER role` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async purchaseSubscription(
    @Query('userId') userId: string,
    @Body() dto: PurchaseSubscriptionDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.purchaseSubscription(userId, dto, sessionData);
  }

  @Patch('switch-role')
  @ApiOperation({ summary: `Switch user role between MEMBER and OWNER` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async switchUserRole(
    @Query('userId') userId: string,
    @Body() dto: SwitchUserRoleDto,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.switchUserRole(userId, dto, sessionData);
  }

  @Delete('cancel-subscription/:communityId')
  @ApiOperation({ summary: `Cancel subscription and handle OWNER role removal` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async cancelSubscription(
    @Query('userId') userId: string,
    @Param('communityId') communityId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.cancelSubscription(userId, communityId, sessionData);
  }

  @Get('subscription-status')
  @ApiOperation({ summary: `Get user's subscription status and available roles` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async getUserSubscriptionStatus(
    @Query('userId') userId: string,
    @GetUserSession() sessionData: UserSessionType,
  ) {
    return this.profileService.getUserSubscriptionStatus(userId, sessionData);
  }

  @Post('handle-expired-subscriptions')
  @ApiOperation({ summary: `Handle expired subscriptions and role cleanup` })
  @ApiQuery({
    name: 'userId',
    type: String,
    required: true,
  })
  async handleExpiredSubscriptions(
    @Query('userId') userId: string,
  ) {
    return this.profileService.handleExpiredSubscriptions(userId);
  }
}
