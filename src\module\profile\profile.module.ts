import { Modu<PERSON> } from '@nestjs/common';
import { ProfileController } from './profile.controller';
import { UserCoreModule } from 'src/core/user-core/user-core.module';
import { AuthModule } from '../auth/auth.module';
import { ProfileService } from './profile.service';
import { CertificateCoreModule } from 'src/core/certificate-core/certificate-core.module';
import { MemberPurchaseCoreModule } from 'src/core/member-purchase-core/member-purchase-core.module';

@Module({
  imports: [UserCoreModule, CertificateCoreModule, MemberPurchaseCoreModule, AuthModule],
  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService],
})
export class ProfileModule {}
