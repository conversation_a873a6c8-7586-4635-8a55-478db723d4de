import {
  Injectable,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { USER_TYPE, STATUS, MEMBERSHIP_STATUS } from '@prisma/client';
import {
  CreateCertificateDto,
  UpdateOwnerProfileDto,
  UpdateMemberProfileDto,
  UpdateNotificationPreferenceDto,
  StartTrialDto,
  PurchaseSubscriptionDto,
  SwitchUserRoleDto,
} from './dto/profile.dto';
import { certificateMessage, userMessage } from 'src/shared/keys/helper.key';
import { UserCoreService } from 'src/core/user-core/user-core.service';
import { CertificateCoreService } from 'src/core/certificate-core/certificate-core.service';
import { MemberPurchaseCoreService } from 'src/core/member-purchase-core/member-purchase-core.service';
import { UserSessionType } from 'src/shared/types/user-session.type';

@Injectable()
export class ProfileService {
  constructor(
    private readonly userCoreService: UserCoreService,
    private readonly certificateCoreService: CertificateCoreService,
    private readonly memberPurchaseCoreService: MemberPurchaseCoreService,
  ) {}

  // Update member profile
  async updateMemberProfile(
    userId: string,
    dto: UpdateMemberProfileDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.MEMBER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    await this.userCoreService.update({
      where: { id: userId },
      data: dto,
    });

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: userMessage.PROFILE_UPDATED,
      user: updatedUser,
    };
  }

  // Update owner profile (only bio and profileImage)
  async updateOwnerProfile(
    userId: string,
    dto: UpdateOwnerProfileDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });
    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    await this.userCoreService.update({
      where: { id: userId },
      data: dto,
    });

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: userMessage.PROFILE_UPDATED,
      user: updatedUser,
    };
  }

  // Add certificate
  async addCertificate(
    userId: string,
    dto: CreateCertificateDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    await this.certificateCoreService.create({
      data: {
        ...dto,
        userId,
      },
    });

    return {
      status: true,
      message: certificateMessage.CERTIFICATE_ADDED,
    };
  }

  // Delete certificate
  async deleteCertificate(
    userId: string,
    certificateId: string,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const cert = await this.certificateCoreService.findUnique({
      where: { id: certificateId },
    });

    if (!cert || cert.userId !== userId) {
      throw new NotFoundException(certificateMessage.CERTIFICATE_NOT_FOUND);
    }

    if (cert.isDeleted) {
      throw new BadRequestException(
        certificateMessage.CERTIFICATE_ALREADY_DELETED,
      );
    }

    await this.certificateCoreService.update({
      where: { id: certificateId },
      data: {
        isDeleted: true,
        status: STATUS.DISABLED,
      },
    });

    return {
      status: true,
      message: certificateMessage.CERTIFICATE_DELETED,
    };
  }

  // Get all certificates of a user
  async getCertificates(userId: string, sessionData: UserSessionType) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.OWNER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const certificates = await this.certificateCoreService.findMany({
      where: {
        userId,
        isDeleted: false,
        status: STATUS.ENABLED,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      status: true,
      data: certificates,
    };
  }

  async updateNotificationPreference(
    userId: string,
    dto: UpdateNotificationPreferenceDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    if (sessionData.user.currentType !== USER_TYPE.MEMBER) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    await this.userCoreService.update({
      where: { id: userId },
      data: {
        isPushNotificationsEnabled: dto.isPushNotificationsEnabled,
      },
    });

    return {
      status: true,
      message: 'Push notification preference updated',
    };
  }

  // Start 14-day free trial and add OWNER role
  async startFreeTrial(
    userId: string,
    dto: StartTrialDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    // Check if user already has OWNER role
    const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];
    if (currentUserTypes.includes('OWNER')) {
      throw new BadRequestException('User already has OWNER access');
    }

    // Check if user already has an active subscription for this community
    const existingPurchase = await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
      userId,
      dto.communityId,
    );

    if (existingPurchase) {
      throw new BadRequestException('User already has an active subscription for this community');
    }

    // Calculate trial expiration date (14 days from now)
    const trialExpiresAt = new Date();
    trialExpiresAt.setDate(trialExpiresAt.getDate() + 14);

    // Create trial purchase record
    await this.memberPurchaseCoreService.create({
      data: {
        memberId: userId,
        communityId: dto.communityId,
        productId: 'trial_14_days',
        platform: 'ANDROID', // Default platform for trial
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
        expiresAt: trialExpiresAt,
      },
    });

    // Add OWNER to userType array
    const updatedUserTypes = [...currentUserTypes, 'OWNER'];
    await this.userCoreService.update({
      where: { id: userId },
      data: {
        userType: updatedUserTypes,
        currentType: USER_TYPE.OWNER, // Switch to OWNER role immediately
      },
    });

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: '14-day free trial started successfully',
      user: updatedUser,
      trialExpiresAt,
    };
  }

  // Handle subscription purchase and add OWNER role
  async purchaseSubscription(
    userId: string,
    dto: PurchaseSubscriptionDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    // Check if user already has an active subscription for this community
    const existingPurchase = await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
      userId,
      dto.communityId,
    );

    if (existingPurchase && existingPurchase.membershipStatus === MEMBERSHIP_STATUS.ACTIVE) {
      throw new BadRequestException('User already has an active subscription for this community');
    }

    // Here you would validate the receipt with the app store
    // For now, we'll assume the purchase is valid
    // In a real implementation, you'd call your IAP service to validate the receipt

    // Create purchase record
    const purchase = await this.memberPurchaseCoreService.create({
      data: {
        memberId: userId,
        communityId: dto.communityId,
        productId: 'monthly_subscription', // This should come from receipt validation
        platform: dto.platform.toUpperCase() as any,
        membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
        // expiresAt would be set based on receipt validation
      },
    });

    // Add OWNER to userType array if not already present
    const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];
    if (!currentUserTypes.includes('OWNER')) {
      const updatedUserTypes = [...currentUserTypes, 'OWNER'];
      await this.userCoreService.update({
        where: { id: userId },
        data: {
          userType: updatedUserTypes,
          currentType: USER_TYPE.OWNER, // Switch to OWNER role immediately
        },
      });
    }

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: 'Subscription purchased successfully',
      user: updatedUser,
      purchase,
    };
  }

  // Switch user role between MEMBER and OWNER
  async switchUserRole(
    userId: string,
    dto: SwitchUserRoleDto,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];

    // Check if user has the target role
    if (!currentUserTypes.includes(dto.targetRole)) {
      throw new BadRequestException(`User does not have ${dto.targetRole} role access`);
    }

    // If switching to OWNER, check if user has active subscription
    if (dto.targetRole === 'OWNER') {
      const hasActiveSubscription = await this.hasActiveSubscription(userId);
      if (!hasActiveSubscription) {
        throw new BadRequestException('User does not have an active subscription to switch to OWNER role');
      }
    }

    // Update current role
    await this.userCoreService.update({
      where: { id: userId },
      data: {
        currentType: dto.targetRole as USER_TYPE,
      },
    });

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: `Successfully switched to ${dto.targetRole} role`,
      user: updatedUser,
    };
  }

  // Handle subscription cancellation and remove OWNER role
  async cancelSubscription(
    userId: string,
    communityId: string,
    sessionData: UserSessionType,
  ) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    // Find and cancel the subscription
    const purchase = await this.memberPurchaseCoreService.findActivePurchaseByUserAndCommunity(
      userId,
      communityId,
    );

    if (!purchase) {
      throw new NotFoundException('No active subscription found for this community');
    }

    // Update purchase status to cancelled
    await this.memberPurchaseCoreService.update({
      where: { id: purchase.id },
      data: { membershipStatus: MEMBERSHIP_STATUS.REVOKED },
    });

    // Check if user has any other active subscriptions
    const hasOtherActiveSubscriptions = await this.hasActiveSubscription(userId, communityId);

    // If no other active subscriptions, remove OWNER role and switch to MEMBER
    if (!hasOtherActiveSubscriptions) {
      const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];
      const updatedUserTypes = currentUserTypes.filter(type => type !== 'OWNER');

      await this.userCoreService.update({
        where: { id: userId },
        data: {
          userType: updatedUserTypes,
          currentType: USER_TYPE.MEMBER, // Switch back to MEMBER role
        },
      });
    }

    const updatedUser = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    return {
      status: true,
      message: 'Subscription cancelled successfully',
      user: updatedUser,
      removedOwnerRole: !hasOtherActiveSubscriptions,
    };
  }

  // Handle expired trials and subscriptions - remove OWNER role if no active subscriptions
  async handleExpiredSubscriptions(userId: string) {
    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    // Check if user has any active subscriptions
    const hasActiveSubscription = await this.hasActiveSubscription(userId);

    // If no active subscriptions and user has OWNER role, remove it
    if (!hasActiveSubscription) {
      const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];
      if (currentUserTypes.includes('OWNER')) {
        const updatedUserTypes = currentUserTypes.filter(type => type !== 'OWNER');

        await this.userCoreService.update({
          where: { id: userId },
          data: {
            userType: updatedUserTypes,
            currentType: USER_TYPE.MEMBER, // Switch back to MEMBER role
          },
        });

        return {
          status: true,
          message: 'OWNER role removed due to expired subscription',
          roleRemoved: true,
        };
      }
    }

    return {
      status: true,
      message: 'No action needed',
      roleRemoved: false,
    };
  }

  // Get user's subscription status and available roles
  async getUserSubscriptionStatus(userId: string, sessionData: UserSessionType) {
    if (userId !== sessionData.user.id) {
      throw new UnauthorizedException(userMessage.NOT_AUTHORIZED);
    }

    const user = await this.userCoreService.findUnique({
      where: { id: userId, isDeleted: false },
    });

    if (!user) {
      throw new NotFoundException(userMessage.USER_NOT_FOUND);
    }

    // Get all user's purchases
    const purchases = await this.memberPurchaseCoreService.findPurchasesByUser(userId);

    // Get active subscriptions
    const activeSubscriptions = purchases.filter(
      (p) =>
        p.membershipStatus === MEMBERSHIP_STATUS.ACTIVE &&
        (!p.expiresAt || p.expiresAt > new Date()),
    );

    const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];
    const hasOwnerAccess = currentUserTypes.includes('OWNER');
    const hasActiveSubscription = activeSubscriptions.length > 0;

    return {
      status: true,
      user: {
        id: user.id,
        currentType: user.currentType,
        availableRoles: currentUserTypes,
        hasOwnerAccess,
        hasActiveSubscription,
      },
      activeSubscriptions,
      totalSubscriptions: purchases.length,
    };
  }

  // Helper method to check if user has any active subscriptions
  private async hasActiveSubscription(userId: string, excludeCommunityId?: string): Promise<boolean> {
    const whereClause: any = {
      memberId: userId,
      membershipStatus: MEMBERSHIP_STATUS.ACTIVE,
      OR: [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ],
    };

    if (excludeCommunityId) {
      whereClause.communityId = { not: excludeCommunityId };
    }

    const activePurchases = await this.memberPurchaseCoreService.findMany({
      where: whereClause,
    });

    return activePurchases.length > 0;
  }
}
