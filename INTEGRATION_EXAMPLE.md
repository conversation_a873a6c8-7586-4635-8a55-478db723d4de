# Integration Example

## How to Integrate with Existing Subscription Service

### 1. Update Subscription Service

You can enhance your existing `SubscriptionService` to work with the new role management:

```typescript
// In subscription.service.ts
import { ProfileService } from '../profile/profile.service';

@Injectable()
export class SubscriptionService {
  constructor(
    // ... existing dependencies
    private readonly profileService: ProfileService,
  ) {}

  async renewSubscription(
    communityId: string,
    receiptData: string,
    platform: 'android' | 'ios',
    sessionData: UserSessionType,
    packageName?: string,
  ) {
    // ... existing renewal logic

    // After successful renewal, ensure user has OWNER role
    const user = await this.userCoreService.findUnique({
      where: { id: sessionData.user.id },
    });

    const currentUserTypes = Array.isArray(user.userType) ? user.userType : [];
    if (!currentUserTypes.includes('OWNER')) {
      const updatedUserTypes = [...currentUserTypes, 'OWNER'];
      await this.userCoreService.update({
        where: { id: sessionData.user.id },
        data: {
          userType: updatedUserTypes,
          currentType: USER_TYPE.OWNER,
        },
      });
    }

    return result;
  }

  async cancelSubscription(communityId: string, sessionData: UserSessionType) {
    // Use the profile service for cancellation to handle role management
    return this.profileService.cancelSubscription(
      sessionData.user.id,
      communityId,
      sessionData,
    );
  }
}
```

### 2. Frontend Integration

#### React Native Example

```typescript
// SubscriptionScreen.tsx
import { useState, useEffect } from 'react';

const SubscriptionScreen = () => {
  const [subscriptionStatus, setSubscriptionStatus] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const fetchSubscriptionStatus = async () => {
    try {
      const response = await api.get(`/profile/subscription-status?userId=${userId}`);
      setSubscriptionStatus(response.data);
    } catch (error) {
      console.error('Failed to fetch subscription status:', error);
    }
  };

  const startFreeTrial = async (communityId: string) => {
    setLoading(true);
    try {
      const response = await api.post(`/profile/start-trial?userId=${userId}`, {
        communityId,
      });
      
      if (response.data.status) {
        // Update local user state
        updateUserRole('OWNER');
        // Navigate to owner features
        navigation.navigate('OwnerDashboard');
      }
    } catch (error) {
      console.error('Failed to start trial:', error);
    } finally {
      setLoading(false);
    }
  };

  const purchaseSubscription = async (communityId: string) => {
    setLoading(true);
    try {
      // Initiate IAP purchase
      const purchase = await RNIap.requestPurchase(productId);
      
      // Send receipt to backend
      const response = await api.post(`/profile/purchase-subscription?userId=${userId}`, {
        communityId,
        platform: Platform.OS,
        receiptData: purchase.transactionReceipt,
        packageName: 'com.yourapp.package',
      });

      if (response.data.status) {
        updateUserRole('OWNER');
        navigation.navigate('OwnerDashboard');
      }
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const switchRole = async (targetRole: 'MEMBER' | 'OWNER') => {
    try {
      const response = await api.patch(`/profile/switch-role?userId=${userId}`, {
        targetRole,
      });
      
      if (response.data.status) {
        updateUserRole(targetRole);
      }
    } catch (error) {
      console.error('Failed to switch role:', error);
    }
  };

  return (
    <View>
      {!subscriptionStatus?.user.hasOwnerAccess ? (
        <View>
          <Button title="Start 14-Day Trial" onPress={() => startFreeTrial(communityId)} />
          <Button title="Purchase Subscription" onPress={() => purchaseSubscription(communityId)} />
        </View>
      ) : (
        <View>
          <Text>Current Role: {subscriptionStatus.user.currentType}</Text>
          <Button 
            title="Switch to Member" 
            onPress={() => switchRole('MEMBER')}
            disabled={subscriptionStatus.user.currentType === 'MEMBER'}
          />
          <Button 
            title="Switch to Owner" 
            onPress={() => switchRole('OWNER')}
            disabled={subscriptionStatus.user.currentType === 'OWNER'}
          />
        </View>
      )}
    </View>
  );
};
```

### 3. Scheduled Job for Expired Subscriptions

```typescript
// scheduled-tasks.service.ts
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ProfileService } from '../profile/profile.service';
import { UserCoreService } from '../core/user-core/user-core.service';

@Injectable()
export class ScheduledTasksService {
  constructor(
    private readonly profileService: ProfileService,
    private readonly userCoreService: UserCoreService,
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  async handleExpiredSubscriptions() {
    console.log('Checking for expired subscriptions...');
    
    // Get all users with OWNER role
    const usersWithOwnerRole = await this.userCoreService.findMany({
      where: {
        userType: {
          array_contains: 'OWNER',
        },
        isDeleted: false,
      },
    });

    for (const user of usersWithOwnerRole) {
      try {
        await this.profileService.handleExpiredSubscriptions(user.id);
      } catch (error) {
        console.error(`Failed to handle expired subscriptions for user ${user.id}:`, error);
      }
    }
  }
}
```

### 4. Middleware for Role-Based Access

```typescript
// role-guard.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const RequireRole = (role: 'MEMBER' | 'OWNER') => SetMetadata('role', role);

// role.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRole = this.reflector.get<string>('role', context.getHandler());
    if (!requiredRole) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    return user && user.currentType === requiredRole;
  }
}

// Usage in controllers
@Controller('owner-features')
@UseGuards(UserLoginJwtGuard, RoleGuard)
export class OwnerFeaturesController {
  
  @Get('dashboard')
  @RequireRole('OWNER')
  async getOwnerDashboard(@GetUserSession() sessionData: UserSessionType) {
    // Only accessible to users with OWNER role
    return { message: 'Owner dashboard data' };
  }
}
```

### 5. Error Handling and User Feedback

```typescript
// error-handler.service.ts
@Injectable()
export class ErrorHandlerService {
  handleSubscriptionError(error: any) {
    if (error.message.includes('already has OWNER access')) {
      return {
        type: 'info',
        message: 'You already have premium access!',
        action: 'redirect_to_owner_features',
      };
    }
    
    if (error.message.includes('active subscription')) {
      return {
        type: 'info',
        message: 'You already have an active subscription for this community.',
        action: 'show_subscription_details',
      };
    }
    
    if (error.message.includes('does not have OWNER role access')) {
      return {
        type: 'error',
        message: 'Please purchase a subscription to access owner features.',
        action: 'show_upgrade_options',
      };
    }
    
    return {
      type: 'error',
      message: 'Something went wrong. Please try again.',
      action: 'retry',
    };
  }
}
```

This integration approach allows you to:

1. **Maintain existing functionality** while adding role management
2. **Handle complex scenarios** like multiple subscriptions and role switching
3. **Provide smooth user experience** with proper error handling
4. **Ensure data consistency** with scheduled cleanup tasks
5. **Implement role-based access control** throughout your app
